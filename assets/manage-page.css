/* Manage Page Specific Styles */

.tab-content {
    margin-top: 20px;
}

.git-actions-bar {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.git-actions-bar .button {
    margin-right: 10px;
}

.file-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 12px;
    color: white;
}

.status-m {
    background-color: #d63638;
}

/* Modified */
.status-a {
    background-color: #00a32a;
}

/* Added */
.status-d {
    background-color: #d63638;
}

/* Deleted */
.status-r {
    background-color: #dba617;
}

/* Renamed */
.status-\? {
    background-color: #666;
}

/* Untracked */

.commit-form,
.branch-actions,
.remote-actions,
.advanced-actions {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.history-controls {
    margin-bottom: 20px;
}

.history-controls select,
.history-controls button {
    margin-left: 10px;
}

#commit-history .commit-item {
    border-bottom: 1px solid #eee;
    padding: 10px 0;
}

#commit-history .commit-item:last-child {
    border-bottom: none;
}

.danger-zone .button {
    margin-right: 10px;
}

/* Commit History Table Styles */
#commit-history .wp-list-table {
    margin-top: 10px;
}

#commit-history .column-hash code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    cursor: pointer;
}

#commit-history .column-hash code:hover {
    background: #e1e1e1;
}

#commit-history .column-message {
    font-weight: 500;
}

#commit-history .column-author {
    color: #666;
    font-size: 13px;
}

#commit-history .column-date {
    color: #666;
    font-size: 13px;
}

#commit-history .column-relative {
    color: #999;
    font-size: 12px;
    font-style: italic;
}

#commit-history tr:hover {
    background-color: #f9f9f9;
}

/* Commit Changes Display Styles */
.git-commit-changes-output {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.git-commit-changes-output h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.commit-changes-content {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 3px;
    padding: 15px;
    font-family: 'Courier New', Monaco, monospace;
    font-size: 13px;
    line-height: 1.4;
    white-space: pre-wrap;
    overflow-x: auto;
    max-height: 500px;
    overflow-y: auto;
}

/* Git diff syntax highlighting */
.commit-changes-content {
    color: #333;
}

/* Danger Zone Styles */
.danger-zone {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 15px;
    margin-top: 20px;
}

.danger-zone h4 {
    margin-top: 0;
    color: #856404;
}

.danger-zone p {
    color: #856404;
}

/* Output Area */
#git-output {
    margin-top: 20px;
}

/* Commit History Table Column Widths */
.column-hash {
    width: 100px;
}

.column-author {
    width: 150px;
}

.column-date {
    width: 120px;
}

.column-relative {
    width: 100px;
}

.column-actions {
    width: 120px;
}
