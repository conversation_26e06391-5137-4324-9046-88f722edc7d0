/* Settings Page Specific Styles */

.setup-guide-section {
    background: #f0f6fc;
    border: 1px solid #0969da;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.setup-guide-section h2 {
    margin-top: 0;
    color: #0969da;
}

.setup-guide-section .dashicons {
    margin-right: 5px;
}

.quick-links-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.quick-links-section .button {
    margin-right: 10px;
}

.quick-links-section .dashicons {
    margin-right: 5px;
}

#action-output {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
    font-family: monospace;
    white-space: pre-wrap;
    max-height: 300px;
    overflow-y: auto;
}

/* Reset Section Styles */
.reset-section {
    margin-top: 40px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 20px;
}

.reset-section h2 {
    margin-top: 0;
    color: #856404;
}

.reset-section p {
    color: #856404;
}

.reset-section .reset-subsection {
    margin-bottom: 20px;
}

.reset-section .reset-subsection-danger {
    border-top: 1px solid #ffeaa7;
    padding-top: 20px;
}

.reset-section .reset-subsection-danger h3 {
    color: #d63384;
}

.reset-section .reset-subsection-danger p {
    color: #d63384;
}

.reset-section .reset-subsection-danger ul {
    color: #d63384;
}

.button-danger {
    background: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

/* Test Result Styles */
.test-result {
    margin-left: 10px;
}

.test-result.success {
    color: #00a32a;
}

.test-result.error {
    color: #d63638;
}
