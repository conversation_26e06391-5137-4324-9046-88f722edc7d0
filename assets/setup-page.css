/* Setup Page Specific Styles */

.setup-breadcrumb {
    margin-bottom: 20px;
    padding: 10px;
    background: #f9f9f9;
    border-left: 4px solid #0073aa;
    border-radius: 4px;
}

.setup-breadcrumb p {
    margin: 0;
}

.hidden {
    display: none;
}

.setup-step {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.setup-step h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

#setup-status {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.status-item {
    margin-bottom: 10px;
    padding: 5px 10px;
    border-radius: 3px;
}

.status-item.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-item.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-item.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

#git-path-status {
    margin-top: 10px;
}

.progress-item {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 3px;
    background: #f1f1f1;
    border: 1px solid #ddd;
}

.progress-item.in-progress {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.progress-item.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.progress-item.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
