<?php

/**
 * Settings page for WP Git Manager
 */
if (!current_user_can('manage_options')) {
    wp_die('Unauthorized');
}

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['wpgm_settings_nonce'], 'wpgm_settings')) {
    $settings = array(
        'wpgm_git_path' => sanitize_text_field($_POST['git_path']),
        'wpgm_repo_path' => sanitize_text_field($_POST['repo_path']),
        'wpgm_remote_name' => sanitize_text_field($_POST['remote_name']),
        'wpgm_branch_name' => sanitize_text_field($_POST['branch_name']),
        'wpgm_auto_add' => isset($_POST['auto_add']) ? '1' : '0',
        'wpgm_commit_author_name' => sanitize_text_field($_POST['commit_author_name']),
        'wpgm_commit_author_email' => sanitize_email($_POST['commit_author_email']),
        'wpgm_default_commit_message' => sanitize_text_field($_POST['default_commit_message'])
    );

    foreach ($settings as $key => $value) {
        update_option($key, $value);
    }

    // Handle .gitignore update
    if (isset($_POST['gitignore_content'])) {
        $gitignore_content = stripslashes($_POST['gitignore_content']);
        $gitignore_path = get_option('wpgm_repo_path', ABSPATH) . '.gitignore';
        file_put_contents($gitignore_path, $gitignore_content);
    }

    echo '<div class="notice notice-success is-dismissible"><p>Settings saved successfully!</p></div>';
}

// Get current settings
$git_path = get_option('wpgm_git_path', '/usr/bin/git');
$repo_path = get_option('wpgm_repo_path', ABSPATH);
$remote_name = get_option('wpgm_remote_name', 'origin');
$branch_name = get_option('wpgm_branch_name', 'main');
$auto_add = get_option('wpgm_auto_add', '1');
$commit_author_name = get_option('wpgm_commit_author_name', '');
$commit_author_email = get_option('wpgm_commit_author_email', '');
$default_commit_message = get_option('wpgm_default_commit_message', 'WordPress changes');

// Load .gitignore content
$gitignore_path = $repo_path . '.gitignore';
$gitignore_content = file_exists($gitignore_path) ? file_get_contents($gitignore_path) : '';
?>

<div class="wrap">
    <h1>Git Manager Settings</h1>

    <!-- Setup Guide Section -->
    <div class="setup-guide-section" style="background: #f0f6fc; border: 1px solid #0969da; border-radius: 6px; padding: 20px; margin-bottom: 20px;">
        <h2 style="margin-top: 0; color: #0969da;">
            <span class="dashicons dashicons-admin-tools"></span> Setup & Configuration
        </h2>
        <p>Need to set up Git for the first time or having issues? Use our guided setup process.</p>
        <p>
            <a href="<?php echo admin_url('admin.php?page=wp-git-manager-setup'); ?>" class="button button-primary">
                <span class="dashicons dashicons-admin-settings"></span> Start Setup Guide
            </a>
        </p>
    </div>

    <form method="post" action="">
        <?php wp_nonce_field('wpgm_settings', 'wpgm_settings_nonce'); ?>

        <!-- Git Configuration Section -->
        <h2>Git Configuration</h2>
        <table class="form-table">
            <tr>
                <th scope="row">Git Path</th>
                <td>
                    <input type="text" name="git_path" value="<?php echo esc_attr($git_path); ?>" class="regular-text" />
                    <button type="button" class="button" id="test-git-path">Test Path</button>
                    <p class="description">Path to the Git executable on your server.</p>
                </td>
            </tr>
            <tr>
                <th scope="row">Repository Path</th>
                <td>
                    <input type="text" name="repo_path" value="<?php echo esc_attr($repo_path); ?>" class="regular-text" />
                    <p class="description">Path to your WordPress installation directory.</p>
                </td>
            </tr>
        </table>

        <!-- Repository Settings Section -->
        <h2>Repository Settings</h2>
        <table class="form-table">
            <tr>
                <th scope="row">Remote Name</th>
                <td>
                    <input type="text" name="remote_name" value="<?php echo esc_attr($remote_name); ?>" class="regular-text" />
                    <p class="description">Name of the remote repository (usually 'origin').</p>
                </td>
            </tr>
            <tr>
                <th scope="row">Branch Name</th>
                <td>
                    <input type="text" name="branch_name" value="<?php echo esc_attr($branch_name); ?>" class="regular-text" />
                    <p class="description">Default branch name (e.g., 'main', 'master', 'develop').</p>
                </td>
            </tr>
        </table>

        <!-- Commit Settings Section -->
        <h2>Commit Settings</h2>
        <table class="form-table">
            <tr>
                <th scope="row">Auto Add Files</th>
                <td>
                    <label>
                        <input type="checkbox" name="auto_add" value="1" <?php checked($auto_add, '1'); ?> />
                        Automatically add all files when committing
                    </label>
                    <p class="description">When enabled, 'git add -A' will be run before each commit.</p>
                </td>
            </tr>
            <tr>
                <th scope="row">Commit Author Name</th>
                <td>
                    <input type="text" name="commit_author_name" value="<?php echo esc_attr($commit_author_name); ?>" class="regular-text" />
                    <p class="description">Name to use for Git commits (leave empty to use global Git config).</p>
                </td>
            </tr>
            <tr>
                <th scope="row">Commit Author Email</th>
                <td>
                    <input type="email" name="commit_author_email" value="<?php echo esc_attr($commit_author_email); ?>" class="regular-text" />
                    <p class="description">Email to use for Git commits (leave empty to use global Git config).</p>
                </td>
            </tr>
            <tr>
                <th scope="row">Default Commit Message</th>
                <td>
                    <input type="text" name="default_commit_message" value="<?php echo esc_attr($default_commit_message); ?>" class="regular-text" />
                    <p class="description">Default message to pre-fill in commit dialogs.</p>
                </td>
            </tr>
        </table>

        <h2>Git Ignore Configuration</h2>
        <table class="form-table">
            <tr>
                <th scope="row">.gitignore Content</th>
                <td>
                    <textarea name="gitignore_content" rows="15" cols="60" class="large-text code"><?php echo esc_textarea($gitignore_content); ?></textarea>
                    <p class="description">Files and patterns to exclude from Git tracking. One pattern per line.</p>
                    <p class="description">
                        <strong>Common WordPress exclusions:</strong><br>
                        <code>wp-config.php</code> - Database credentials<br>
                        <code>wp-content/uploads/</code> - Media files<br>
                        <code>*.log</code> - Log files<br>
                        <code>.DS_Store</code> - macOS system files<br>
                        <code>node_modules/</code> - Node.js dependencies
                    </p>
                </td>
            </tr>
        </table>

        <!-- Connection Testing -->
        <h2>Connection Testing</h2>
        <table class="form-table">
            <tr>
                <th scope="row">Test Configuration</th>
                <td>
                    <p>
                        <button type="button" class="button" id="test-connection">Test Git Connection</button>
                        <span class="description">Verify that Git is properly configured and accessible.</span>
                    </p>
                    <div id="action-output"></div>
                </td>
            </tr>
        </table>

        <?php submit_button(); ?>
    </form>

    <!-- Reset Plugin Data Section -->
    <div class="reset-section" style="margin-top: 40px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 20px;">
        <h2 style="margin-top: 0; color: #856404;">⚠️ Reset Plugin Data</h2>
        <p style="color: #856404;">These operations will permanently remove data. Use with extreme caution.</p>

        <div style="margin-bottom: 20px;">
            <h3>Reset Git Repository Only</h3>
            <p>This will remove the .git directory and basic Git configuration, but keep your plugin settings.</p>
            <button type="button" class="button button-secondary" id="reset-git-only">Reset Git Repository</button>
        </div>

        <div style="border-top: 1px solid #ffeaa7; padding-top: 20px;">
            <h3 style="color: #d63384;">Complete Plugin Reset</h3>
            <p style="color: #d63384;"><strong>WARNING:</strong> This will remove ALL plugin data including:</p>
            <ul style="color: #d63384;">
                <li>Git repository (.git directory)</li>
                <li>All plugin settings and configuration</li>
                <li>.gitignore file</li>
                <li>Plugin will be reset to initial state</li>
            </ul>
            <button type="button" class="button button-secondary" id="reset-all-data" style="background: #dc3545; border-color: #dc3545; color: white;">Complete Reset</button>
        </div>
    </div>

</div>

<style>
    .setup-guide-section {
        background: #f0f6fc;
        border: 1px solid #0969da;
        border-radius: 6px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .setup-guide-section h2 {
        margin-top: 0;
        color: #0969da;
    }

    .setup-guide-section .dashicons {
        margin-right: 5px;
    }

    .quick-links-section {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .quick-links-section .button {
        margin-right: 10px;
    }

    .quick-links-section .dashicons {
        margin-right: 5px;
    }

    #action-output {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        margin-top: 10px;
        font-family: monospace;
        white-space: pre-wrap;
        max-height: 300px;
        overflow-y: auto;
    }
</style>

<?php
// Enqueue settings-page-specific JavaScript
wp_enqueue_script(
    'wp-git-manager-settings-page',
    plugin_dir_url(__FILE__) . '../assets/settings-page.js',
    array('jquery', 'wp-git-manager-main'),
    '1.0.0',
    true
);
?>