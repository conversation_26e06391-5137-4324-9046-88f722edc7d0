<?php

/**
 * Setup page for WP Git Manager
 */

if (!current_user_can('manage_options')) {
    wp_die('Unauthorized');
}
?>

<div class="wrap">
    <div class="setup-breadcrumb" style="margin-bottom: 20px; padding: 10px; background: #f9f9f9; border-left: 4px solid #0073aa; border-radius: 4px;">
        <p style="margin: 0;">
            <a href="<?php echo admin_url('admin.php?page=wp-git-manager'); ?>">Git Manager</a> &raquo;
            <a href="<?php echo admin_url('admin.php?page=wp-git-manager-settings'); ?>">Settings</a> &raquo;
            <strong>Setup Guide</strong>
        </p>
    </div>

    <h1>Git Manager Setup</h1>

    <div id="setup-container">
        <div class="setup-step" id="step-1">
            <h2>Step 1: Git Configuration</h2>
            <p>First, let's verify that Git is available on your server.</p>

            <table class="form-table">
                <tr>
                    <th scope="row">Git Path</th>
                    <td>
                        <input type="text" id="git-path" class="regular-text" value="<?php echo esc_attr(get_option('wpgm_git_path', '/usr/bin/git')); ?>" />
                        <p class="description">Path to the Git executable on your server.</p>
                        <div id="git-path-status"></div>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Repository Path</th>
                    <td>
                        <input type="text" id="repo-path" class="regular-text" value="<?php echo esc_attr(get_option('wpgm_repo_path', ABSPATH)); ?>" />
                        <p class="description">Path to your WordPress installation directory.</p>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <button type="button" class="button button-primary" id="test-git">Test Git Path</button>
                <button type="button" class="button" id="check-setup" style="display:none;">Check Setup Status</button>
            </p>
        </div>

        <div class="setup-step" id="step-2" style="display:none;">
            <h2>Step 2: Repository Configuration</h2>
            <p>Please provide the following information to set up your Git repository:</p>

            <div id="setup-status"></div>

            <div id="config-form">
                <h3>Git User Configuration</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">User Name</th>
                        <td>
                            <input type="text" id="git-user-name" class="regular-text" placeholder="Your Name" required />
                            <p class="description">Your name for Git commits</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">User Email</th>
                        <td>
                            <input type="email" id="git-user-email" class="regular-text" placeholder="<EMAIL>" required />
                            <p class="description">Your email for Git commits</p>
                        </td>
                    </tr>
                </table>

                <h3>Initial Branch Configuration</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Branch Name</th>
                        <td>
                            <input type="text" id="branch-name" class="regular-text" value="main" required />
                            <p class="description">Name for the initial branch (e.g., main, master, develop)</p>
                        </td>
                    </tr>
                </table>

                <h3>Remote Repository (Optional)</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Remote Name</th>
                        <td>
                            <input type="text" id="remote-name" class="regular-text" value="origin" />
                            <p class="description">Name for the remote repository</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Remote URL</th>
                        <td>
                            <input type="url" id="remote-url" class="regular-text" placeholder="https://github.com/username/repo.git" />
                            <p class="description">Optional: Add a remote repository URL (GitHub, GitLab, etc.)</p>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <button type="button" class="button button-primary" id="start-setup">Initialize Repository</button>
                </p>
            </div>

            <div id="setup-progress" style="display:none;">
                <h3>Setting up repository...</h3>
                <div id="progress-status">
                    <div class="progress-item" id="progress-init">○ Initializing repository...</div>
                    <div class="progress-item" id="progress-user">○ Setting user configuration...</div>
                    <div class="progress-item" id="progress-remote">○ Adding remote repository...</div>
                    <div class="progress-item" id="progress-files">○ Creating initial files...</div>
                </div>
            </div>
        </div>

        <div class="setup-step" id="step-3" style="display:none;">
            <h2>Step 3: Final Configuration</h2>
            <p>Save your settings and complete the setup.</p>

            <div id="final-settings">
                <table class="form-table">
                    <tr>
                        <th scope="row">Auto-add Files</th>
                        <td>
                            <label>
                                <input type="checkbox" id="auto-add" checked />
                                Automatically add all files when committing
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Exclude Files</th>
                        <td>
                            <textarea id="gitignore-content" rows="10" cols="50" placeholder="wp-config.php&#10;*.log&#10;node_modules/&#10;.DS_Store"></textarea>
                            <p class="description">Files and patterns to exclude from Git (will create/update .gitignore)</p>
                        </td>
                    </tr>
                </table>
            </div>

            <p class="submit">
                <button type="button" class="button button-primary" id="complete-setup">Complete Setup</button>
            </p>
        </div>

        <div id="setup-complete" style="display:none;">
            <div class="notice notice-success">
                <p><strong>Setup Complete!</strong> Your Git repository is now ready to use.</p>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager'); ?>" class="button button-primary">
                        <span class="dashicons dashicons-dashboard"></span> Go to Dashboard
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager-settings'); ?>" class="button">
                        <span class="dashicons dashicons-admin-settings"></span> Go to Settings
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager-manage'); ?>" class="button">
                        <span class="dashicons dashicons-admin-tools"></span> Manage Repository
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

<style>
    .setup-step {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .setup-step h2 {
        margin-top: 0;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }

    #setup-status {
        background: #f1f1f1;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .status-item {
        margin-bottom: 10px;
        padding: 5px 10px;
        border-radius: 3px;
    }

    .status-item.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-item.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .status-item.warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    #git-path-status {
        margin-top: 10px;
    }

    .progress-item {
        margin-bottom: 10px;
        padding: 8px 12px;
        border-radius: 3px;
        background: #f1f1f1;
        border: 1px solid #ddd;
    }

    .progress-item.in-progress {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .progress-item.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .progress-item.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
</style>

<?php
// Enqueue setup-page-specific JavaScript
wp_enqueue_script(
    'wp-git-manager-setup-page',
    plugin_dir_url(__FILE__) . '../assets/setup-page.js',
    array('jquery', 'wp-git-manager-main'),
    '1.0.0',
    true
);
?>